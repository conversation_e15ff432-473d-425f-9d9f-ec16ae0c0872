import React, { useState } from 'react';
import {
  Box,
  Typography,
  Paper,
  ToggleButton,
  ToggleButtonGroup
} from '@mui/material';
import {
  Class as ClassIcon,
  ViewList as ViewListIcon,
  Category as CategoryIcon
} from '@mui/icons-material';
import BackButton from './BackButton';
import ScoringProgressCard from './ScoringProgressCard';
import TopGroupsSection from './TopGroupsSection';
import SortingControls from './SortingControls';
import GroupsTable from './GroupsTable';
import CategorizedGroupsView from './CategorizedGroupsView';

const GroupsView = ({
  selectedClassification,
  processedGroups,
  sortConfig,
  requestSort,
  isAutoSelectLoading,
  handleAutoSelectTopGroups,
  handleMovesOnChange,
  handleGroupSelect,
  onBackToClassificationSummary,
  onBackToSummary
}) => {
  // State for view mode
  const [viewMode, setViewMode] = useState('table');

  const handleViewModeChange = (event, newViewMode) => {
    if (newViewMode !== null) {
      setViewMode(newViewMode);
    }
  };
  if (!selectedClassification || !selectedClassification.groups) {
    return (
      <Paper sx={{ p: 3, textAlign: 'center' }}>
        <Typography variant="body1">
          No groups found in this classification.
        </Typography>
      </Paper>
    );
  }

  // Helper function to format classification name with phase
  const formatClassificationName = (classification) => {
    const baseName = classification.id?.name || 'Classification';
    if (classification.phase) {
      return `${baseName} - Phase ${classification.phase}`;
    }
    return baseName;
  };

  // Calculate scoring statistics
  const totalGroups = processedGroups.length;
  const scoredGroups = processedGroups.filter(group => group.hasScore).length;
  const movesOnGroups = processedGroups.filter(group => group.movesOn).length;
  const progress = totalGroups > 0 ? (scoredGroups / totalGroups) * 100 : 0;

  return (
    <Box sx={{ p: 3 }}>
      <Box sx={{ display: 'flex', gap: 2 }}>
        <BackButton
          onClick={onBackToClassificationSummary}
          text="Back to Classification Summary"
        />
        <BackButton
          onClick={onBackToSummary}
          text="Back to Summary"
        />
      </Box>

      <Typography variant="h5" sx={{ mb: 3, color: '#3b8c6e', display: 'flex', alignItems: 'center' }}>
        <ClassIcon sx={{ mr: 1 }} />
        {formatClassificationName(selectedClassification)} Groups
      </Typography>

      {/* Classification Summary Card */}
      <ScoringProgressCard 
        totalGroups={totalGroups}
        scoredGroups={scoredGroups}
        progress={progress}
        phase={selectedClassification.phase}
        movesOnGroups={movesOnGroups}
      />

      {/* Top 50% Groups Section - Only show for Phase 1 */}
      {selectedClassification.phase === 1 && (
        <TopGroupsSection
          sortedGroups={processedGroups}
          isAutoSelectLoading={isAutoSelectLoading}
          handleAutoSelectTopGroups={handleAutoSelectTopGroups}
          handleMovesOnChange={handleMovesOnChange}
        />
      )}

      {/* View Controls */}
      <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', mb: 3 }}>
        <Typography variant="body1">
          Select a group to begin scoring.
        </Typography>

        <Box sx={{ display: 'flex', gap: 2, alignItems: 'center' }}>
          {/* View Mode Toggle */}
          <ToggleButtonGroup
            value={viewMode}
            exclusive
            onChange={handleViewModeChange}
            size="small"
            sx={{ mr: 2 }}
          >
            <ToggleButton value="table" aria-label="table view">
              <ViewListIcon sx={{ mr: 1 }} />
              Table
            </ToggleButton>
            <ToggleButton value="categorized" aria-label="categorized view">
              <CategoryIcon sx={{ mr: 1 }} />
              Categories
            </ToggleButton>
          </ToggleButtonGroup>

          {/* Sorting Controls - Only show in table view */}
          {viewMode === 'table' && (
            <SortingControls
              sortConfig={sortConfig}
              requestSort={requestSort}
              showMovesOnSort={selectedClassification.phase === 1}
            />
          )}
        </Box>
      </Box>

      {/* Conditional Rendering based on view mode */}
      {viewMode === 'table' ? (
        <GroupsTable
          groups={processedGroups}
          handleGroupSelect={handleGroupSelect}
          handleMovesOnChange={handleMovesOnChange}
          showMovesOn={selectedClassification.phase === 1}
          showActionButton={true}
        />
      ) : (
        <CategorizedGroupsView
          groups={processedGroups}
          handleGroupSelect={handleGroupSelect}
          handleMovesOnChange={handleMovesOnChange}
          showMovesOn={selectedClassification.phase === 1}
          showActionButton={true}
          isPhase1={selectedClassification.phase === 1}
        />
      )}
    </Box>
  );
};

export default GroupsView;
